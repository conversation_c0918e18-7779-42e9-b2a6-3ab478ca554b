.content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 3.5em;
    color: #333;
    margin: 0;
    margin-bottom: 10px;
}

.header .subtitle {
    font-size: 1.2em;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

body {
    margin: 0;
    min-height: 100vh;
    background-color: #f0f0f0;
    font-family: Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 20px;
}

#canvas-container {
    position: relative;
    display: inline-block;
    border: 2px solid #333;
    border-radius: 4px;
    background: white;
}

.canvas-stack {
    position: relative;
    width: 800px;
    height: 600px;
}

#background-canvas {
    position: absolute;
    left: 0;
    top: 0;
}

#drawing-canvas {
    position: absolute;
    left: 0;
    top: 0;
}

.toolbar {
    position: absolute;
    left: 10px;
    top: 10px;
    background: white;
    padding: 10px;
    border-radius: 4px;
    box-shadow: 0 0 5px rgba(0,0,0,0.2);
    z-index: 3;
}

#settings-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    font-size: 24px;
    background: white;
    border-radius: 50%;
    padding: 5px;
    box-shadow: 0 0 5px rgba(0,0,0,0.2);
    z-index: 3;
}

.tool-button {
    margin: 5px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    background: #4CAF50;
    color: white;
    font-weight: bold;
}

.tool-button:hover {
    background: #45a049;
}

.tool-button:disabled {
    background: #cccccc;
    cursor: not-allowed;
    opacity: 0.7;
}

.tool-button.active {
    background: #357a38;
}

.modal {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
    min-width: 300px;
    z-index: 1000;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
    margin: 10px 0;
}

.color-option {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.selected {
    border: 2px solid #000;
}

.section-title {
    margin: 15px 0 5px 0;
    font-weight: bold;
}

.input-group {
    margin: 10px 0;
}

#background-input {
    width: 100%;
    padding: 8px;
    margin-top: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}
