FROM python:3.9-slim

RUN apt-get update && apt-get install -y \
    nginx \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY app.py .
COPY templates/ templates/
COPY static/ static/

COPY nginx.conf /etc/nginx/nginx.conf

ENV FLAG="FAKE_FLAG{FOR_TESTING}"

EXPOSE 80

CMD ["sh", "-c", "service nginx start && gunicorn --bind 127.0.0.1:5000 app:app"]
