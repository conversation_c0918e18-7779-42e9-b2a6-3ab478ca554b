const drawingCanvas = document.getElementById('drawing-canvas');
const backgroundCanvas = document.getElementById('background-canvas');
const ctx = drawingCanvas.getContext('2d');
const bgCtx = backgroundCanvas.getContext('2d');

let isDrawing = false;
let currentTool = 'draw';
let lastX = 0;
let lastY = 0;

const DRAW_SIZE = 5;
const ERASE_SIZE = 20;

let history = [];
let currentStep = -1;
const maxHistory = 50;

ctx.fillStyle = 'rgba(0,0,0,0)';
ctx.fillRect(0, 0, drawingCanvas.width, drawingCanvas.height);

bgCtx.fillStyle = 'white';
bgCtx.fillRect(0, 0, backgroundCanvas.width, backgroundCanvas.height);

function setTool(tool) {
    currentTool = tool;
    document.querySelectorAll('.tool-button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function saveState() {
    if (currentStep < history.length - 1) {
        history = history.slice(0, currentStep + 1);
    }
    currentStep++;
    history.push(ctx.getImageData(0, 0, drawingCanvas.width, drawingCanvas.height));
    
    if (history.length > maxHistory) {
        history.shift();
        currentStep--;
    }
    
    updateUndoRedoButtons();
}

function updateUndoRedoButtons() {
    document.getElementById('undo-button').disabled = currentStep === 0;
    document.getElementById('redo-button').disabled = currentStep === history.length - 1;
}

function undo() {
    if (currentStep > 0) {
        currentStep--;
        ctx.putImageData(history[currentStep], 0, 0);
        updateUndoRedoButtons();
    }
}

function redo() {
    if (currentStep < history.length - 1) {
        currentStep++;
        ctx.putImageData(history[currentStep], 0, 0);
        updateUndoRedoButtons();
    }
}

function clearCanvas() {
    ctx.clearRect(0, 0, drawingCanvas.width, drawingCanvas.height);
    saveState();
}

drawingCanvas.addEventListener('mousedown', startDrawing);
drawingCanvas.addEventListener('mousemove', draw);
drawingCanvas.addEventListener('mouseup', stopDrawing);
drawingCanvas.addEventListener('mouseout', stopDrawing);

document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'z') {
        e.preventDefault();
        undo();
    } else if (e.ctrlKey && e.key === 'y') {
        e.preventDefault();
        redo();
    }
});

function startDrawing(e) {
    isDrawing = true;
    const rect = drawingCanvas.getBoundingClientRect();
    lastX = e.clientX - rect.left;
    lastY = e.clientY - rect.top;
}

function draw(e) {
    if (!isDrawing) return;
    
    const rect = drawingCanvas.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const currentY = e.clientY - rect.top;
    
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(currentX, currentY);
    ctx.lineWidth = currentTool === 'draw' ? DRAW_SIZE : ERASE_SIZE;
    ctx.lineCap = 'round';

    if (currentTool === 'draw') {
        ctx.globalCompositeOperation = 'source-over';
        ctx.strokeStyle = 'black';
    } else {
        ctx.globalCompositeOperation = 'destination-out';
        ctx.strokeStyle = 'rgba(0,0,0,1)';
    }
    
    ctx.stroke();
    
    lastX = currentX;
    lastY = currentY;
}

function stopDrawing() {
    if (isDrawing) {
        isDrawing = false;
        ctx.globalCompositeOperation = 'source-over';
        saveState();
    }
}

function toggleSettings() {
    const modal = document.getElementById('settings-modal');
    modal.style.display = modal.style.display === "none" || modal.style.display === "" ? "block" : "none";
}

function setBackgroundColor(color) {
    bgCtx.fillStyle = color;
    bgCtx.fillRect(0, 0, backgroundCanvas.width, backgroundCanvas.height);
    toggleSettings();
}

function setBackgroundFromUrl() {
    const background = document.getElementById('background-input').value;
    
    fetch('/set_background', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ background: background })
    })
    .then(response => response.json())
    .then(data => {
        if (data.background.startsWith('data:')) {
            const img = new Image();
            img.onload = function() {
                bgCtx.clearRect(0, 0, backgroundCanvas.width, backgroundCanvas.height);
                bgCtx.drawImage(img, 0, 0, backgroundCanvas.width, backgroundCanvas.height);
                toggleSettings();
            };
            img.src = data.background;
        } else {
            setBackgroundColor(data.background);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toggleSettings();
    });
}

saveState();
