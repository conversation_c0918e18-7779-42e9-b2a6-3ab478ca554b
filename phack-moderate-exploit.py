import string

# Step 1: Copy-pasted from the binary
encoded = (
    "9b8 JFCr n] 1n8l.2edlBD#2N9J8z|@`(1C5.3;008u`2RJtBRn4i018TS:Hf~6'p5;8Wk)"
    "L=<U,y6L8f[DLkDmWJ7x918U uVTA\\I`^1A9M8>v'~Fu7WI238>_*C^PhDe3v8ZIiB+|OB@4"
    "U948?6sHrF*'H5)82-ZUCr$\"l6W8ot=u^2/iX7d9D8&G)b^ }o^'1z8?RoL3u6a}2S8H#w#n="
    "M>#3=9j8Vu(&a/P!c48058NEhE+kk~!5m8'qq[8)vH&6=82B(~_z|NZ7J8@V4%5pFCi1w9L 8)"
    "D.Caj{=h28008Qj$EJ\\!B(3b098,8F80ymm[429B807~g]@?mh5T9v8nFyy'5r2x6&038j4)m"
    "g@(bl7J8Ev0!9BZhv1;9y 8H6K2`~*[|239m8LBh- (hOh3R078#&uh$kxi/4a038Zp+F-/f<"
    "k5m9L8+%OHA\\|OE6v028\\?M6Ojb6<7s8mj'B#HqSi1U8 VKu4({M;@2M9Q8)M[nrHon)3504"
    "8:A|RA^>H#4H958gl>2JfaQq5p9q8u 4){2*bo6I028(eV-)s3qf7C008TA^F\\nT*01E8t Eu"
    "w0dw Q2!80\\ FIu`nW3P88Cz6SNDCd4J9z8QJiJS?$Z]5U8'QdoHw-He6~068S~O3:$oX,7p0"
    "08{3a%'^}  1f8U3i\\Q <O[p2V8M#N\\0PZ`L3K9m81\\W]w;+MZ4D9a8MKA3-:@P75t9R8B$8 "
    "v[kK$9x"
)

# Step 2: ROT13 letters only
def rot13(s):
    return s.translate(str.maketrans(
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",
        "NOPQRSTUVWXYZABCDEFGHIJKLMnopqrstuvwxyzabcdefghijklm"
    ))

decoded = rot13(encoded)

# Step 3: Simulate FUN_0010124f
flag = ""
i = 0
while i < len(decoded):
    ch = decoded[i]
    if '1' <= ch <= '7':
        i += 1
        if i < len(decoded):
            flag += decoded[i]
    elif ch == '8':
        if i > 0 and decoded[i - 1] != '0':
            i += 9
    elif ch == '9':
        break
    i += 1

print(f"🔥 Flag: phack{{{flag}}}")
