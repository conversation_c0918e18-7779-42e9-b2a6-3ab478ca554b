from flask import Flask, render_template, request, jsonify
import subprocess
import base64
from urllib.parse import unquote
import os

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/set_background', methods=['POST'])
def set_background():
    try:
        background = request.json.get('background', '')
        
        if not (background.startswith('#') or background.startswith('rgb') or background.replace(' ', '').isalpha()):
            try:
                result = subprocess.run(
                    ['curl', '-s', '-L', background],
                    capture_output=True,
                    shell=False
                )
                
                if result.returncode == 0 and result.stdout:
                    encoded = base64.b64encode(result.stdout).decode('utf-8')
                    return jsonify({
                        'background': f'data:image/png;base64,{encoded}'
                    })
                return jsonify({'background': ''})
            except Exception:
                return jsonify({'background': ''})
        
        return jsonify({'background': background})
    except Exception:
        return jsonify({'background': ''})

if __name__ == '__main__':
    app.run(debug=False)
