<!DOCTYPE html>
<html>
<head>
    <title>Be Creative!!!!</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <div class="content-wrapper">
        <div class="header">
            <h1>Artful</h1>
            <p class="subtitle">Welcome to the most simple drawing experience of your life. Let your creativity flow below :D</p>
        </div>
        <div id="canvas-container">
            <div class="canvas-stack">
                <canvas id="background-canvas" width="800" height="600"></canvas>
                <canvas id="drawing-canvas" width="800" height="600"></canvas>
            </div>
            <div class="toolbar">
                <button class="tool-button active" onclick="setTool('draw')">Draw</button>
                <button class="tool-button" onclick="setTool('erase')">Erase</button>
                <button class="tool-button" id="undo-button" onclick="undo()" disabled>Undo</button>
                <button class="tool-button" id="redo-button" onclick="redo()" disabled>Redo</button>
                <button class="tool-button" onclick="clearCanvas()">Clear</button>
            </div>
            <div id="settings-icon" onclick="toggleSettings()">⚙️</div>
        </div>

    </div>
    <div id="settings-modal" class="modal" style="display: none;">
        <h3 style="margin-top: 0;">Background Settings</h3>
        
        <div class="section-title">Select Color:</div>
        <div class="color-grid">
            <div class="color-option" style="background: #FFFFFF" onclick="setBackgroundColor('#FFFFFF')"></div>
            <div class="color-option" style="background: #F8B195" onclick="setBackgroundColor('#F8B195')"></div>
            <div class="color-option" style="background: #F67280" onclick="setBackgroundColor('#F67280')"></div>
            <div class="color-option" style="background: #C06C84" onclick="setBackgroundColor('#C06C84')"></div>
            <div class="color-option" style="background: #6C5B7B" onclick="setBackgroundColor('#6C5B7B')"></div>
            <div class="color-option" style="background: #355C7D" onclick="setBackgroundColor('#355C7D')"></div>
            <div class="color-option" style="background: #99B898" onclick="setBackgroundColor('#99B898')"></div>
            <div class="color-option" style="background: #FECEAB" onclick="setBackgroundColor('#FECEAB')"></div>
            <div class="color-option" style="background: #FF847C" onclick="setBackgroundColor('#FF847C')"></div>
            <div class="color-option" style="background: #E84A5F" onclick="setBackgroundColor('#E84A5F')"></div>
        </div>

        <div class="section-title">- OR -</div>
        
        <div class="input-group">
            <div class="section-title">Set Background:</div>
            <div style="font-size: 0.9em; color: #666; margin-bottom: 8px;">
                Enter a hex color (e.g., #FF5733) or URL to a PNG image
            </div>
            <input type="text" id="background-input" placeholder="Example: #FF5733 or https://placehold.co/600x400.png">
        </div>
        
        <button class="tool-button" onclick="setBackgroundFromUrl()">Apply</button>
        <button class="tool-button" style="background: #666" onclick="toggleSettings()">Close</button>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
