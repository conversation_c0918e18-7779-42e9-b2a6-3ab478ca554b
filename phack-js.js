'use strict';

const Koa = require('koa');
const Router = require('@koa/router');
const render = require('koa-ejs');
const { koaBody } = require('koa-body');
const vm = require('node:vm');

const app = new Koa();
const router = new Router();

render(app, {
    root: __dirname,
    layout: 'index',
    viewExt: 'html',
    cache: false,
    debug: false
})

router.get('/', (ctx) => {
    return ctx.render('index', { code: "// Write your code here!\n1+1;", result: null });
});

router.post('/', koaBody(), (ctx) => {
    if (!ctx.request.body || !ctx.request.body.code || typeof(ctx.request.body.code) != 'string') {
        return ctx.render('index', { code: "", result: "Error: no code provided!" });
    }
    const code = ctx.request.body.code
    try {
        const res = vm.runInContext(code, vm.createContext({}), {timeout: 1000})
        return ctx.render('index', { code: code, result: res });
    } catch(err) {
        return ctx.render('index', { code: code, result: "Error when running the provided code: " + err.toString()});
    }
});

app.use(router.routes());
app.use(router.allowedMethods());

app.listen(1234);
